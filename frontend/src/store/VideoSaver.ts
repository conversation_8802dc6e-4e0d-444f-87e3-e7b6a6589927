import { EditorElement, AudioEditorElement } from "../types";
import { isEditorAudioElement } from "../utils";
import { CONSTANTS } from "./constants";

export class VideoSaver {
  private canvas: HTMLCanvasElement;
  private editorElements: EditorElement[];
  private maxTime: number;
  private selectedVideoFormat: string;

  constructor(
    canvas: HTMLCanvasElement,
    editorElements: EditorElement[],
    maxTime: number,
    selectedVideoFormat: string
  ) {
    this.canvas = canvas;
    this.editorElements = editorElements;
    this.maxTime = maxTime;
    this.selectedVideoFormat = selectedVideoFormat;
  }

  saveCanvasToVideoWithAudio() {
    this.saveCanvasToVideoWithAudioWebmMp4();
  }

  private saveCanvasToVideoWithAudioWebmMp4() {
    console.log("modified");
    let mp4 = this.selectedVideoFormat === "mp4";
    const stream = this.canvas.captureStream(CONSTANTS.MEDIA.CAPTURE_FPS);
    const audioElements = this.editorElements.filter(isEditorAudioElement);
    const audioStreams: MediaStream[] = [];
    audioElements.forEach((audio) => {
      const audioElement = document.getElementById(
        audio.properties.elementId
      ) as HTMLAudioElement;
      let ctx = new AudioContext();
      let sourceNode = ctx.createMediaElementSource(audioElement);
      let dest = ctx.createMediaStreamDestination();
      sourceNode.connect(dest);
      sourceNode.connect(ctx.destination);
      audioStreams.push(dest.stream);
    });
    audioStreams.forEach((audioStream) => {
      stream.addTrack(audioStream.getAudioTracks()[0]);
    });
    const video = document.createElement("video");
    video.srcObject = stream;
    video.height = CONSTANTS.MEDIA.DEFAULT_VIDEO_HEIGHT_RECORD;
    video.width = CONSTANTS.MEDIA.DEFAULT_VIDEO_WIDTH;
    video.play().then(() => {
      const mediaRecorder = new MediaRecorder(stream);
      const chunks: Blob[] = [];
      mediaRecorder.ondataavailable = function (e) {
        chunks.push(e.data);
        console.log("data available");
      };
      mediaRecorder.onstop = async function (e) {
        const blob = new Blob(chunks, { type: "video/webm" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "video.webm";
        a.click();
      };
      mediaRecorder.start();
      setTimeout(() => {
        mediaRecorder.stop();
      }, this.maxTime);
      video.remove();
    });
  }
}
