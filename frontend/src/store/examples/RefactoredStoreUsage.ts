/**
 * 重构后Store架构使用示例
 * 展示如何使用新的架构来初始化和使用Store
 */

import { globalManagerRegistry } from '../core/ManagerRegistry';
import { globalEventBus } from '../core/EventBus';
import { StateManager } from '../core/StateManager';
import { RefactoredStore } from '../RefactoredStore';
import { elementFactory, animationFactory } from '../factories';

// 示例：如何注册和使用Manager
export async function setupRefactoredStore() {
  console.log('🚀 开始设置重构后的Store架构...');

  // 1. 注册StateManager
  globalManagerRegistry.registerManager(
    'StateManager',
    (dependencies, eventBus, config) => new StateManager(dependencies, eventBus, config),
    {
      name: 'StateManager',
      priority: 1, // 最高优先级，最先初始化
      dependencies: [],
      singleton: true
    }
  );

  // 2. 注册其他Manager（示例）
  // 注意：实际的Manager需要继承BaseManager并实现相应接口
  
  // globalManagerRegistry.registerManager(
  //   'ElementManager',
  //   (dependencies, eventBus, config) => new RefactoredElementManager(dependencies, eventBus, config),
  //   {
  //     name: 'ElementManager',
  //     priority: 2,
  //     dependencies: ['StateManager'],
  //     singleton: true
  //   }
  // );

  // globalManagerRegistry.registerManager(
  //   'TimelineManager',
  //   (dependencies, eventBus, config) => new RefactoredTimelineManager(dependencies, eventBus, config),
  //   {
  //     name: 'TimelineManager',
  //     priority: 3,
  //     dependencies: ['StateManager'],
  //     singleton: true
  //   }
  // );

  // 3. 创建Store实例
  const store = new RefactoredStore({
    debugMode: true,
    enableLogging: true
  });

  // 4. 初始化Store（这会自动初始化所有Manager）
  await store.initialize();

  console.log('✅ 重构后的Store架构设置完成');
  
  return store;
}

// 示例：如何使用事件总线进行解耦通信
export function setupEventListeners() {
  console.log('🎧 设置事件监听器...');

  // 监听元素添加事件
  globalEventBus.on('element:added', (data) => {
    console.log('📝 元素已添加:', data.payload.element);
    
    // 可以在这里触发其他操作，如自动保存、更新UI等
    globalEventBus.emit('system:auto-save-triggered', {
      reason: 'element-added',
      elementId: data.payload.element.id
    });
  });

  // 监听Canvas尺寸变化事件
  globalEventBus.on('canvas:resized', (data) => {
    console.log('📐 Canvas尺寸已变化:', data.payload);
    
    // 可以在这里处理自适应缩放等逻辑
    globalEventBus.emit('elements:adaptive-scaling-needed', {
      oldSize: { width: data.payload.oldWidth, height: data.payload.oldHeight },
      newSize: { width: data.payload.width, height: data.payload.height }
    });
  });

  // 监听动画相关事件
  globalEventBus.on('animation:added', (data) => {
    console.log('🎬 动画已添加:', data.payload.animation);
  });

  // 监听系统事件
  globalEventBus.on('system:loading-changed', (data) => {
    console.log('⏳ 加载状态变化:', data.payload);
  });

  console.log('✅ 事件监听器设置完成');
}

// 示例：如何使用工厂模式创建元素
export function demonstrateFactoryUsage() {
  console.log('🏭 演示工厂模式使用...');

  // 创建文本元素
  const textElement = elementFactory.createTextElement({
    text: "Hello World",
    fontSize: 32,
    fontColor: "#FFFFFF",
    placement: {
      x: 100,
      y: 100,
      width: 300,
      height: 80
    }
  });

  console.log('📝 创建的文本元素:', textElement);

  // 创建形状元素
  const shapeElement = elementFactory.createShapeElement({
    shapeType: "rectangle",
    fill: "#FF0000",
    placement: {
      x: 200,
      y: 200,
      width: 150,
      height: 100
    }
  });

  console.log('🔷 创建的形状元素:', shapeElement);

  // 创建动画
  const fadeInAnimation = animationFactory.createFadeInAnimation(textElement.id, 1000);
  console.log('🎬 创建的淡入动画:', fadeInAnimation);

  // 应用动画预设
  const bounceAnimations = animationFactory.applyPreset("Bounce In", shapeElement.id);
  console.log('🎪 应用的弹跳动画预设:', bounceAnimations);

  console.log('✅ 工厂模式演示完成');

  return {
    textElement,
    shapeElement,
    fadeInAnimation,
    bounceAnimations
  };
}

// 示例：完整的使用流程
export async function completeUsageExample() {
  console.log('🎯 开始完整的使用示例...');

  try {
    // 1. 设置架构
    const store = await setupRefactoredStore();
    
    // 2. 设置事件监听
    setupEventListeners();
    
    // 3. 演示工厂使用
    const { textElement, shapeElement, fadeInAnimation } = demonstrateFactoryUsage();
    
    // 4. 使用Store操作
    console.log('📊 开始Store操作演示...');
    
    // 添加元素
    store.addEditorElement(textElement);
    store.addEditorElement(shapeElement);
    
    // 添加动画
    store.addAnimation(fadeInAnimation);
    
    // 选择元素
    store.setSelectedElement(textElement);
    
    // 设置Canvas尺寸
    await store.setCanvasSize(1920, 1080);
    
    // 订阅状态变化
    const unsubscribe = store.subscribe((state) => {
      console.log('📈 Store状态已更新:', {
        elementsCount: state.editorElements.length,
        animationsCount: state.animations.length,
        selectedElement: state.selectedElement?.id,
        canvasSize: { width: state.canvasWidth, height: state.canvasHeight }
      });
    });
    
    // 5. 模拟一些操作
    setTimeout(() => {
      console.log('⏰ 执行延迟操作...');
      
      // 更新元素
      const updatedTextElement = { ...textElement, name: "Updated Text" };
      store.updateEditorElement(updatedTextElement);
      
      // 设置加载状态
      store.setLoading(true, "正在保存...");
      
      setTimeout(() => {
        store.setLoading(false);
        console.log('💾 保存完成');
      }, 2000);
      
    }, 1000);
    
    // 6. 清理（在实际应用中，这通常在组件卸载时调用）
    setTimeout(async () => {
      console.log('🧹 开始清理...');
      unsubscribe();
      await store.destroy();
      console.log('✅ 清理完成');
    }, 5000);
    
    console.log('✅ 完整使用示例设置完成');
    
    return store;
    
  } catch (error) {
    console.error('❌ 使用示例失败:', error);
    throw error;
  }
}

// 示例：如何扩展架构
export class CustomManager {
  // 这是一个示例，展示如何创建自定义Manager
  // 实际使用时应该继承BaseManager
  
  constructor(
    private dependencies: any,
    private eventBus: any,
    private config: any
  ) {}

  async initialize() {
    console.log('🔧 CustomManager 初始化');
    
    // 监听相关事件
    this.eventBus.on('element:added', this.handleElementAdded.bind(this));
  }

  async destroy() {
    console.log('🗑️ CustomManager 销毁');
  }

  private handleElementAdded(data: any) {
    console.log('🎯 CustomManager 处理元素添加事件:', data.payload.element.id);
    
    // 执行自定义逻辑
    this.eventBus.emit('custom:element-processed', {
      elementId: data.payload.element.id,
      processedAt: Date.now()
    });
  }
}

// 注册自定义Manager的示例
export function registerCustomManager() {
  globalManagerRegistry.registerManager(
    'CustomManager',
    (dependencies, eventBus, config) => new CustomManager(dependencies, eventBus, config),
    {
      name: 'CustomManager',
      priority: 10,
      dependencies: ['StateManager'],
      singleton: true
    }
  );
  
  console.log('📝 CustomManager 已注册');
}

// 导出主要函数供外部使用
export {
  setupRefactoredStore,
  setupEventListeners,
  demonstrateFactoryUsage,
  completeUsageExample,
  registerCustomManager
};
