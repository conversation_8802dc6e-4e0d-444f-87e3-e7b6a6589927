import { fabric } from "fabric";
import {
  EditorElement,
  VideoEditorElement,
  ImageEditorElement,
  GifEditorElement,
  TextEditorElement,
  ShapeEditorElement,
} from "../../types";
import { Store } from "../Store";
import {
  CoverImage,
  CoverVideo,
  TextboxWithPadding,
} from "../../utils/fabric-utils";
import { fabricGif } from "../../utils/fabricGif";
import { ShapeFactory } from "../../utils/ShapeFactory";
import { CONSTANTS, FILTER_METHOD_MAP, BASIC_STYLE_UPDATES } from "../constants";

/**
 * 元素渲染器类 - 负责将元素渲染到Fabric.js画布上
 */
export class ElementRenderer {
  private store: Store;
  private canvas: fabric.Canvas;

  constructor(store: Store) {
    this.store = store;
  }

  setCanvas(canvas: fabric.Canvas) {
    this.canvas = canvas;
  }

  // ==================== 主要渲染方法 ====================

  /**
   * 添加元素到画布
   */
  async addElement(element: EditorElement): Promise<void> {
    if (!this.canvas) return;

    this._ensureMediaElementExists(element);

    const objectModifiedHandler = this._createObjectModifiedHandler();
    const elementAddMethods = {
      video: () => this._addVideoElement(element as VideoEditorElement, objectModifiedHandler),
      image: () => this._addImageElement(element as ImageEditorElement, objectModifiedHandler),
      gif: () => this._addGifElement(element as GifEditorElement, objectModifiedHandler),
      text: () => this._addTextElement(element as TextEditorElement, objectModifiedHandler),
      shape: () => this._addShapeElement(element as ShapeEditorElement, objectModifiedHandler),
      audio: () => {}, // Audio elements don't have fabric objects
    };

    const addMethod = elementAddMethods[element.type];
    if (addMethod) {
      // 对于GIF元素，等待异步创建完成
      if (element.type === "gif") {
        await addMethod();
      } else {
        addMethod();
      }
      this._setupElementSelection(element);
    } else {
      throw new Error(`Element type ${element.type} not implemented`);
    }
  }

  /**
   * 刷新元素显示
   */
  refreshElement(element: EditorElement) {
    if (!this.canvas || !element.fabricObject) return;

    // 更新fabric对象的属性
    this._updateFabricObjectProperties(element);
    
    // 重新渲染画布
    this.canvas.requestRenderAll();
  }

  // ==================== 视频元素渲染 ====================

  private _addVideoElement(element: VideoEditorElement, modifiedHandler: (e: fabric.IEvent) => void) {
    const videoElement = document.getElementById(element.properties.elementId) as HTMLVideoElement;
    if (!videoElement) return;

    const videoObject = new CoverVideo(videoElement, {
      ...this._getCommonMediaFabricCreationProperties(element),
      objectCaching: true,
    });

    element.fabricObject = videoObject;
    this.canvas.add(videoObject);
    videoObject.on("modified", modifiedHandler);
  }

  // ==================== 图片元素渲染 ====================

  private _addImageElement(element: ImageEditorElement, modifiedHandler: (e: fabric.IEvent) => void) {
    const imageElement = document.getElementById(element.properties.elementId) as HTMLImageElement;
    if (!imageElement) return;

    const imageObject = new CoverImage(imageElement, {
      ...this._getCommonMediaFabricCreationProperties(element),
      objectCaching: true,
    });

    element.fabricObject = imageObject;
    this.canvas.add(imageObject);
    imageObject.on("modified", modifiedHandler);
  }

  // ==================== GIF元素渲染 ====================

  private async _addGifElement(element: GifEditorElement, modifiedHandler: (e: fabric.IEvent) => void) {
    const gifElement = document.getElementById(element.properties.elementId) as HTMLImageElement;
    if (!gifElement) return;

    try {
      const gifObject = await fabricGif(gifElement.src, {
        ...this._getCommonMediaFabricCreationProperties(element),
        objectCaching: false,
      });

      // 确保GIF对象有滤镜支持
      this._ensureGifFilterSupport(gifObject);

      element.fabricObject = gifObject;
      this.canvas.add(gifObject);
      gifObject.on("modified", modifiedHandler);
    } catch (error) {
      console.error("Failed to create GIF object:", error);
    }
  }

  // ==================== 文本元素渲染 ====================

  private _addTextElement(element: TextEditorElement, modifiedHandler: (e: fabric.IEvent) => void) {
    const textObject = new TextboxWithPadding(element.properties.text, {
      ...this._getTextFabricCreationProperties(element),
      objectCaching: false,
    });

    // 应用高级样式（渐变、阴影）
    this._applyTextAdvancedStyles(textObject, element.properties);

    element.fabricObject = textObject;
    this.canvas.add(textObject);
    textObject.on("modified", modifiedHandler);
  }

  // ==================== 形状元素渲染 ====================

  private _addShapeElement(element: ShapeEditorElement, modifiedHandler: (e: fabric.IEvent) => void) {
    const shapeObject = ShapeFactory.createShapeObject(
      element.properties.shapeType,
      {
        ...this._getCommonElementProperties(element),
        fill: element.properties.fill,
        stroke: element.properties.stroke,
        strokeWidth: element.properties.strokeWidth,
      }
    );

    if (shapeObject) {
      element.fabricObject = shapeObject;
      this.canvas.add(shapeObject);
      shapeObject.on("modified", modifiedHandler);
    }
  }

  // ==================== 私有辅助方法 ====================

  private _createObjectModifiedHandler() {
    return (e: fabric.IEvent) => {
      if (!e.target) return;

      const modifiedElement = this.store.editorElements.find(
        (el) => el.fabricObject === e.target
      );
      if (!modifiedElement) return;

      const updatedElement = this.store.elementManager.syncFabricPropertiesToElement(
        modifiedElement,
        e.target
      );
      this.store.updateEditorElement(updatedElement);
    };
  }

  private _setupElementSelection(element: EditorElement) {
    if (!element.fabricObject) return;
    
    // 设置元素选择状态
    this.store.setSelectedElement(element);
    this.canvas.setActiveObject(element.fabricObject);
  }

  private _ensureMediaElementExists(element: EditorElement) {
    if (element.type === "audio") return; // 音频元素不需要DOM元素

    const elementId = element.properties.elementId;
    if (!document.getElementById(elementId)) {
      console.warn(`Media element with id ${elementId} not found in DOM`);
    }
  }

  private _getCommonElementProperties(element: EditorElement) {
    return {
      left: element.placement?.x || 0,
      top: element.placement?.y || 0,
      width: element.placement?.width || 100,
      height: element.placement?.height || 100,
      angle: element.placement?.rotation || 0,
      scaleX: element.placement?.scaleX || 1,
      scaleY: element.placement?.scaleY || 1,
      flipX: element.placement?.flipX || false,
      flipY: element.placement?.flipY || false,
      opacity: element.opacity || 1,
    };
  }

  private _getCommonMediaFabricCreationProperties(element: EditorElement) {
    return {
      ...this._getCommonElementProperties(element),
      selectable: true,
      hasControls: true,
      hasBorders: true,
      lockUniScaling: false,
    };
  }

  private _getTextFabricCreationProperties(element: TextEditorElement) {
    const props = element.properties;
    const styles = props.styles || [];

    return {
      ...this._getCommonElementProperties(element),
      fontSize: props.fontSize,
      fontFamily: props.fontFamily || "Arial",
      textAlign: props.textAlign || "left",
      lineHeight: props.lineHeight || 1,
      charSpacing: props.charSpacing || 0,
      fontWeight: (styles.includes("bold") || props.fontWeight === 700 ? "bold" : "normal") as "normal" | "bold",
      fontStyle: (styles.includes("italic") ? "italic" : "normal") as "" | "normal" | "italic" | "oblique",
      underline: styles.includes("underlined"),
      linethrough: styles.includes("strikethrough"),
      strokeWidth: props.strokeWidth || 0,
      stroke: props.strokeColor || "#000000",
      backgroundColor: props.backgroundColor,
    };
  }

  private _applyTextAdvancedStyles(textObject: fabric.TextboxWithPadding, properties: any) {
    // 应用渐变
    if (properties.useGradient && properties.gradientColors?.length >= 2) {
      const gradient = new fabric.Gradient({
        type: "linear",
        coords: { x1: 0, y1: 0, x2: 0, y2: textObject.height || 100 },
        colorStops: [
          { offset: 0, color: properties.gradientColors[0] },
          { offset: 1, color: properties.gradientColors[1] },
        ],
      });
      textObject.set("fill", gradient);
    } else {
      textObject.set("fill", properties.fontColor || "#ffffff");
    }

    // 应用阴影
    if (properties.shadowBlur > 0) {
      textObject.set("shadow", new fabric.Shadow({
        color: properties.shadowColor || "#000000",
        blur: properties.shadowBlur,
        offsetX: properties.shadowOffsetX || 0,
        offsetY: properties.shadowOffsetY || 0,
      }));
    }
  }

  private _updateFabricObjectProperties(element: EditorElement) {
    if (!element.fabricObject) return;

    // 更新通用属性
    element.fabricObject.set({
      left: element.placement?.x || 0,
      top: element.placement?.y || 0,
      width: element.placement?.width || 100,
      height: element.placement?.height || 100,
      angle: element.placement?.rotation || 0,
      scaleX: element.placement?.scaleX || 1,
      scaleY: element.placement?.scaleY || 1,
      flipX: element.placement?.flipX || false,
      flipY: element.placement?.flipY || false,
      opacity: element.opacity || 1,
    });

    // 根据元素类型更新特定属性
    if (element.type === "text") {
      this._updateTextProperties(element as TextEditorElement);
    }
  }

  private _updateTextProperties(element: TextEditorElement) {
    const textObject = element.fabricObject as fabric.TextboxWithPadding;
    if (!textObject) return;

    // 应用基础样式更新
    Object.entries(BASIC_STYLE_UPDATES).forEach(([styleKey, fabricKey]) => {
      const value = element.properties[styleKey as keyof typeof element.properties];
      if (value !== undefined) {
        textObject.set(fabricKey as any, value);
      }
    });

    // 重新应用高级样式
    this._applyTextAdvancedStyles(textObject, element.properties);
  }

  private _ensureGifFilterSupport(gifObject: fabric.Image) {
    // 为GIF对象添加滤镜方法支持
    Object.entries(FILTER_METHOD_MAP).forEach(([filterType, methodName]) => {
      if (typeof (gifObject as any)[methodName] !== "function") {
        (gifObject as any)[methodName] = function(value: number) {
          // 实现基本的滤镜功能
          console.log(`Applying ${filterType} filter with value ${value} to GIF`);
        };
      }
    });
  }
}
