import { fabric } from "fabric";
import { EditorElement, Placement, TextEditorElement, TimeFrame } from "../../types";
import { Store } from "../Store";
import { ELEMENT_OPERATION_CONSTANTS } from "../constants";

/**
 * 元素同步器类 - 负责Fabric对象与EditorElement之间的数据同步
 */
export class ElementSynchronizer {
  private store: Store;

  constructor(store: Store) {
    this.store = store;
  }

  // ==================== 主要同步方法 ====================

  /**
   * 将Fabric对象的属性同步到EditorElement
   */
  syncFabricPropertiesToElement(element: EditorElement, fabricObject: fabric.Object): EditorElement {
    const updatedElement = { ...element };

    // 同步位置和变换属性
    updatedElement.placement = {
      ...updatedElement.placement,
      ...this._createPlacementFromFabricObject(fabricObject),
    };

    // 处理缩放逻辑
    const isTextElement = element.type === "text";
    const currentFontSize = isTextElement ? (element as TextEditorElement).properties.fontSize : undefined;
    
    const { placement, fontSize } = this._handleScaling(
      fabricObject,
      updatedElement.placement,
      isTextElement,
      currentFontSize
    );

    updatedElement.placement = { ...updatedElement.placement, ...placement };

    // 对于文本元素，更新字体大小
    if (isTextElement && fontSize !== undefined) {
      (updatedElement as TextEditorElement).properties.fontSize = fontSize;
    }

    return updatedElement;
  }

  /**
   * 将EditorElement的属性同步到Fabric对象
   */
  syncElementToFabricObject(element: EditorElement): void {
    if (!element.fabricObject) return;

    const fabricObject = element.fabricObject;

    // 同步基本属性
    fabricObject.set({
      left: element.placement?.x || 0,
      top: element.placement?.y || 0,
      width: element.placement?.width || 100,
      height: element.placement?.height || 100,
      angle: element.placement?.rotation || 0,
      scaleX: element.placement?.scaleX || 1,
      scaleY: element.placement?.scaleY || 1,
      flipX: element.placement?.flipX || false,
      flipY: element.placement?.flipY || false,
      opacity: element.opacity || 1,
    });

    // 根据元素类型同步特定属性
    if (element.type === "text") {
      this._syncTextElementProperties(element as TextEditorElement);
    }

    // 更新坐标
    fabricObject.setCoords();
  }

  /**
   * 同步Fabric对象到Element（用于保存状态）
   */
  syncFabricObjectToElement(element: EditorElement): void {
    if (!element.fabricObject) return;

    const fabricObject = element.fabricObject;

    // 更新placement
    element.placement = {
      ...element.placement,
      ...this._createPlacementFromFabricObject(fabricObject),
    };

    // 处理文本元素的特殊同步
    if (element.type === "text") {
      this._syncFabricTextToElement(element as TextEditorElement);
    }
  }

  // ==================== 时间帧更新 ====================

  /**
   * 更新元素时间帧
   */
  updateElementTimeFrame(
    element: EditorElement,
    newTimeFrame: TimeFrame,
    isDragEnd: boolean = false
  ): void {
    const oldTimeFrame = { ...element.timeFrame };
    element.timeFrame = newTimeFrame;

    // 只在拖拽结束或非拖拽状态下执行重渲染
    if (isDragEnd || !this.store.isDraggingTimeFrame) {
      // 检查是否真的需要刷新所有元素
      const needsFullRefresh = this._needsFullElementRefresh(
        element,
        oldTimeFrame,
        element.timeFrame
      );

      if (needsFullRefresh) {
        this.store.elementManager.refreshElements();
      } else {
        // 只更新当前元素的可见性和位置
        this._updateSingleElementVisibility(element);
        this.store.canvas?.requestRenderAll();
      }

      this.store.updateMediaElements();

      // 如果是拖拽结束，保存更改并更新最大时间
      if (isDragEnd) {
        // 更新最大时间，确保播放和indicator不超过最大endtime
        this.store.updateMaxTime();

        // 开始分组操作，将连续的时间帧调整视为一个操作
        if (!this.store.historyManager.isGrouping) {
          this.store.startHistoryGroup("Move Element");
        }
        this.store.saveChange("Move Element");

        // 延迟结束分组，允许短时间内的连续操作被视为一组
        setTimeout(() => {
          this.store.endHistoryGroup();
        }, 500);
      }
    }
  }

  // ==================== 私有辅助方法 ====================

  private _createPlacementFromFabricObject(fabricObject: fabric.Object): Partial<Placement> {
    return {
      x: fabricObject.left,
      y: fabricObject.top,
      width: fabricObject.width,
      height: fabricObject.height,
      rotation: fabricObject.angle,
      scaleX: fabricObject.scaleX,
      scaleY: fabricObject.scaleY,
      flipX: fabricObject.flipX,
      flipY: fabricObject.flipY,
    };
  }

  private _hasSignificantScaling(scaleX: number, scaleY: number): boolean {
    return (
      Math.abs(scaleX - 1) > ELEMENT_OPERATION_CONSTANTS.SCALING_THRESHOLD ||
      Math.abs(scaleY - 1) > ELEMENT_OPERATION_CONSTANTS.SCALING_THRESHOLD
    );
  }

  private _handleScaling(
    target: fabric.Object,
    currentPlacement: Placement,
    isTextElement: boolean = false,
    currentFontSize?: number
  ): { placement: Partial<Placement>; fontSize?: number } {
    const scaleX = target.scaleX || 1;
    const scaleY = target.scaleY || 1;
    const newPlacement: Partial<Placement> = { ...currentPlacement };
    let fontSize = currentFontSize;

    // 对于文本元素，保持原始宽度和高度，不应用缩放
    if (isTextElement) {
      // 保持原始尺寸，不乘以缩放因子
      if (target.width !== undefined) newPlacement.width = target.width;
      if (target.height !== undefined) newPlacement.height = target.height;

      // 始终保留缩放值，不重置
      newPlacement.scaleX = scaleX;
      newPlacement.scaleY = scaleY;

      // 不修改字体大小，保持原始值
      // fontSize 保持不变
    } else {
      // 对于非文本元素，保持原有逻辑
      if (target.width !== undefined) newPlacement.width = target.width * scaleX;
      if (target.height !== undefined) newPlacement.height = target.height * scaleY;

      const hasSignificantScaling = this._hasSignificantScaling(scaleX, scaleY);

      if (hasSignificantScaling && !isTextElement) {
        // 对于非文本元素，重置缩放因子
        newPlacement.scaleX = 1;
        newPlacement.scaleY = 1;
      } else {
        newPlacement.scaleX = scaleX;
        newPlacement.scaleY = scaleY;
      }
    }

    return { placement: newPlacement, fontSize };
  }

  private _syncTextElementProperties(element: TextEditorElement): void {
    const textObject = element.fabricObject as fabric.TextboxWithPadding;
    if (!textObject) return;

    const props = element.properties;
    const styles = props.styles || [];

    // 同步文本属性
    textObject.set({
      text: props.text,
      fontSize: props.fontSize,
      fontFamily: props.fontFamily || "Arial",
      textAlign: props.textAlign || "left",
      lineHeight: props.lineHeight || 1,
      charSpacing: props.charSpacing || 0,
      fontWeight: (styles.includes("bold") || props.fontWeight === 700 ? "bold" : "normal") as "normal" | "bold",
      fontStyle: (styles.includes("italic") ? "italic" : "normal") as "" | "normal" | "italic" | "oblique",
      underline: styles.includes("underlined"),
      linethrough: styles.includes("strikethrough"),
      strokeWidth: props.strokeWidth || 0,
      stroke: props.strokeColor || "#000000",
      backgroundColor: props.backgroundColor,
    });

    // 应用高级样式
    this._applyTextAdvancedStyles(textObject, props);
  }

  private _syncFabricTextToElement(element: TextEditorElement): void {
    const textObject = element.fabricObject as fabric.TextboxWithPadding;
    if (!textObject) return;

    // 同步文本内容
    if (textObject.text !== element.properties.text) {
      element.properties.text = textObject.text || "";
    }

    // 同步其他可能在编辑过程中改变的属性
    element.properties.fontSize = textObject.fontSize || element.properties.fontSize;
  }

  private _applyTextAdvancedStyles(textObject: fabric.TextboxWithPadding, properties: any): void {
    // 应用渐变
    if (properties.useGradient && properties.gradientColors?.length >= 2) {
      const gradient = new fabric.Gradient({
        type: "linear",
        coords: { x1: 0, y1: 0, x2: 0, y2: textObject.height || 100 },
        colorStops: [
          { offset: 0, color: properties.gradientColors[0] },
          { offset: 1, color: properties.gradientColors[1] },
        ],
      });
      textObject.set("fill", gradient);
    } else {
      textObject.set("fill", properties.fontColor || "#ffffff");
    }

    // 应用阴影
    if (properties.shadowBlur > 0) {
      textObject.set("shadow", new fabric.Shadow({
        color: properties.shadowColor || "#000000",
        blur: properties.shadowBlur,
        offsetX: properties.shadowOffsetX || 0,
        offsetY: properties.shadowOffsetY || 0,
      }));
    }
  }

  private _needsFullElementRefresh(
    element: EditorElement,
    oldTimeFrame: TimeFrame,
    newTimeFrame: TimeFrame
  ): boolean {
    // 如果时间帧发生了显著变化，需要完整刷新
    const timeChange = Math.abs(newTimeFrame.start - oldTimeFrame.start) + 
                      Math.abs(newTimeFrame.end - oldTimeFrame.end);
    
    // 如果时间变化超过100ms，或者跨越了当前播放时间，需要完整刷新
    return timeChange > 100 || 
           (oldTimeFrame.start <= this.store.currentTimeInMs && newTimeFrame.start > this.store.currentTimeInMs) ||
           (oldTimeFrame.end >= this.store.currentTimeInMs && newTimeFrame.end < this.store.currentTimeInMs);
  }

  private _updateSingleElementVisibility(element: EditorElement): void {
    if (!element.fabricObject) return;

    const currentTime = this.store.currentTimeInMs;
    const isVisible = currentTime >= element.timeFrame.start && currentTime <= element.timeFrame.end;
    
    element.fabricObject.set("visible", isVisible);
  }
}
