import { EditorElement, Placement, TimeFrame, ShapeType } from "../../types";
import { getUid } from "../../utils";
import { ShapeFactory } from "../../utils/ShapeFactory";
import { CONSTANTS, ELEMENT_TYPES } from "../constants";

/**
 * 元素工厂类 - 负责创建各种类型的元素
 */
export class ElementFactory {
  
  // ==================== 基础元素创建 ====================

  /**
   * 创建基础元素结构
   */
  static createBaseElement(
    id: string,
    type: string,
    name: string,
    timeFrame: TimeFrame,
    placement: Placement,
    properties: any
  ): EditorElement {
    return {
      id,
      locked: false,
      opacity: 1,
      name,
      type: type as any,
      placement,
      timeFrame,
      properties,
    };
  }

  /**
   * 创建居中放置的位置信息
   */
  static createCenteredPlacement(width: number, height: number): Placement {
    const canvasWidth = CONSTANTS.CANVAS.DEFAULT_WIDTH;
    const canvasHeight = CONSTANTS.CANVAS.DEFAULT_HEIGHT;
    
    return {
      x: (canvasWidth - width) / 2,
      y: (canvasHeight - height) / 2,
      width,
      height,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      flipX: false,
      flipY: false,
    };
  }

  // ==================== 媒体元素创建 ====================

  /**
   * 创建媒体元素属性
   */
  static createMediaProperties(
    elementId: string,
    src: string,
    originalDuration?: number
  ) {
    const baseProperties = {
      elementId,
      src,
      effect: { type: "none" },
      filters: { type: "none" },
      border: {
        width: 0,
        color: "black",
        style: "solid",
        borderRadius: 0,
      },
    };

    if (originalDuration !== undefined) {
      return { ...baseProperties, originalDuration };
    }

    return baseProperties;
  }

  /**
   * 创建视频元素
   */
  static createVideoElement(
    videoElement: HTMLVideoElement,
    elementId: string,
    videoMetadata?: any
  ): EditorElement {
    const aspectRatio = videoElement.videoWidth / videoElement.videoHeight;
    const videoHeight = CONSTANTS.MEDIA.DEFAULT_VIDEO_HEIGHT;
    const videoWidth = videoHeight * aspectRatio;

    // 优先使用元数据中的名称，否则使用默认名称
    let displayName = `Video ${Date.now()}`;
    if (videoMetadata?.name) {
      displayName = videoMetadata.name;
    }

    return this.createBaseElement(
      elementId,
      ELEMENT_TYPES.VIDEO,
      displayName,
      {
        start: 0,
        end: Math.min(
          videoElement.duration * 1000,
          CONSTANTS.TIMELINE.MAX_TIME
        ),
      },
      this.createCenteredPlacement(videoWidth, videoHeight),
      {
        ...this.createMediaProperties(
          `video-${elementId}`,
          videoElement.src,
          videoElement.duration * 1000
        ),
        hasAudio: !videoElement.muted,
      }
    );
  }

  /**
   * 创建音频元素
   */
  static createAudioElement(
    audioElement: HTMLAudioElement,
    elementId: string,
    audioMetadata?: any
  ): EditorElement {
    // 优先使用元数据中的名称，否则使用默认名称
    let displayName = `Audio ${Date.now()}`;
    if (audioMetadata?.name) {
      displayName = audioMetadata.name;
    }

    return this.createBaseElement(
      elementId,
      ELEMENT_TYPES.AUDIO,
      displayName,
      {
        start: 0,
        end: Math.min(
          audioElement.duration * 1000,
          CONSTANTS.TIMELINE.MAX_TIME
        ),
      },
      this.createCenteredPlacement(100, 50), // 音频元素不需要显示尺寸
      {
        ...this.createMediaProperties(
          `audio-${elementId}`,
          audioElement.src,
          audioElement.duration * 1000
        ),
      }
    );
  }

  /**
   * 创建图片元素
   */
  static createImageElement(
    imageElement: HTMLImageElement,
    elementId: string,
    imageMetadata?: any
  ): EditorElement {
    const aspectRatio = imageElement.naturalWidth / imageElement.naturalHeight;
    const imageSize = CONSTANTS.MEDIA.DEFAULT_IMAGE_SIZE;
    const imageWidth = imageSize * aspectRatio;
    const imageHeight = imageSize;

    // 优先使用元数据中的名称，否则使用默认名称
    let displayName = `Image ${Date.now()}`;
    if (imageMetadata?.name) {
      displayName = imageMetadata.name;
    }

    return this.createBaseElement(
      elementId,
      ELEMENT_TYPES.IMAGE,
      displayName,
      {
        start: 0,
        end: CONSTANTS.TIMELINE.DEFAULT_ELEMENT_DURATION.IMAGE,
      },
      this.createCenteredPlacement(imageWidth, imageHeight),
      this.createMediaProperties(`image-${elementId}`, imageElement.src)
    );
  }

  /**
   * 创建GIF元素
   */
  static createGifElement(
    gifElement: HTMLImageElement,
    elementId: string,
    gifMetadata?: any
  ): EditorElement {
    const aspectRatio = gifElement.naturalWidth / gifElement.naturalHeight;
    const gifSize = CONSTANTS.MEDIA.DEFAULT_IMAGE_SIZE;
    const gifWidth = gifSize * aspectRatio;
    const gifHeight = gifSize;

    // 优先使用元数据中的名称，否则使用默认名称
    let displayName = `GIF ${Date.now()}`;
    if (gifMetadata?.name) {
      displayName = gifMetadata.name;
    }

    return this.createBaseElement(
      elementId,
      ELEMENT_TYPES.GIF,
      displayName,
      {
        start: 0,
        end: CONSTANTS.TIMELINE.DEFAULT_ELEMENT_DURATION.IMAGE,
      },
      this.createCenteredPlacement(gifWidth, gifHeight),
      {
        ...this.createMediaProperties(`gif-${elementId}`, gifElement.src),
        isAnimated: true,
        frameCount: gifMetadata?.frameCount || 1,
        frameRate: gifMetadata?.frameRate || 10,
        originalDuration: gifMetadata?.duration || CONSTANTS.TIMELINE.DEFAULT_ELEMENT_DURATION.IMAGE,
      }
    );
  }

  // ==================== 文本元素创建 ====================

  /**
   * 创建文本元素属性
   */
  static createTextProperties(
    text: string,
    fontSize: number,
    fontWeight: number,
    fontFamily: string = "Arial",
    fontColor: string = "#ffffff",
    textAlign: "left" | "center" | "right" = "left",
    lineHeight: number = 1,
    charSpacing: number = 0,
    styles: string[] = [],
    strokeWidth: number = 0,
    strokeColor: string = "#000000",
    shadowBlur: number = 0,
    shadowOffsetX: number = 0,
    shadowOffsetY: number = 0,
    shadowColor: string = "#000000",
    useGradient: boolean = false,
    gradientColors: string[] = ["#ffffff", "#000000"],
    backgroundColor?: string
  ) {
    return {
      text,
      fontSize,
      fontWeight,
      fontFamily,
      fontColor,
      textAlign,
      lineHeight,
      charSpacing,
      styles,
      strokeWidth,
      strokeColor,
      shadowBlur,
      shadowOffsetX,
      shadowOffsetY,
      shadowColor,
      useGradient,
      gradientColors,
      backgroundColor,
      splittedTexts: [],
    };
  }

  // ==================== 形状元素创建 ====================

  /**
   * 创建形状元素
   */
  static createShapeElement(
    shapeType: ShapeType,
    options?: {
      fill?: string;
      stroke?: string;
      strokeWidth?: number;
      borderRadius?: number;
      id?: string;
    }
  ): EditorElement {
    const elementId = options?.id || getUid();
    const { width, height } = ShapeFactory.getShapeDimensions(shapeType);

    return this.createBaseElement(
      elementId,
      ELEMENT_TYPES.SHAPE,
      `Shape ${Date.now()}`,
      {
        start: 0,
        end: CONSTANTS.TIMELINE.DEFAULT_ELEMENT_DURATION.SHAPE,
      },
      this.createCenteredPlacement(width, height),
      ShapeFactory.createShapeProperties(shapeType, options)
    );
  }
}
