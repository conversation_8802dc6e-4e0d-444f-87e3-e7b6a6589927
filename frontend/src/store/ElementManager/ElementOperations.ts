import { fabric } from "fabric";
import { EditorElement, Placement } from "../../types";
import { Store } from "../Store";
import { ELEMENT_OPERATION_CONSTANTS } from "../constants";

/**
 * 元素操作类 - 负责元素的基本操作（对齐、锁定、克隆等）
 */
export class ElementOperations {
  private store: Store;
  private canvas: fabric.Canvas;

  constructor(store: Store) {
    this.store = store;
  }

  setCanvas(canvas: fabric.Canvas) {
    this.canvas = canvas;
  }

  // ==================== 对齐操作 ====================
  
  private static readonly ALIGNMENT_ACTIONS = {
    left: (object: fabric.Object) => object.set({ left: 0 }),
    center: (object: fabric.Object) => object.centerH(),
    right: (object: fabric.Object, canvas: fabric.Canvas) => {
      const width = object.width || 0;
      const scaleX = object.scaleX || 1;
      object.set({ left: canvas.width - width * scaleX });
    },
    top: (object: fabric.Object) => object.set({ top: 0 }),
    middle: (object: fabric.Object) => object.centerV(),
    bottom: (object: fabric.Object, canvas: fabric.Canvas) => {
      const height = object.height || 0;
      const scaleY = object.scaleY || 1;
      object.set({ top: canvas.height - height * scaleY });
    },
    justify: (object: fabric.Object) => object.center(),
  } as const;

  /**
   * 对齐元素
   */
  alignElement(id: string, alignType: string) {
    const element = this._findElementById(id);
    if (!element?.fabricObject || !this._ensureCanvas()) return;

    const action = ElementOperations.ALIGNMENT_ACTIONS[alignType as keyof typeof ElementOperations.ALIGNMENT_ACTIONS];
    if (!action) return;

    action(element.fabricObject, this.canvas);
    element.placement = {
      ...element.placement,
      ...this._createPlacementFromFabricObject(element.fabricObject),
    };
    this.canvas.renderAll();
  }

  // ==================== 锁定操作 ====================

  /**
   * 切换元素锁定状态
   */
  toggleLockElement(id: string) {
    const element = this._findElementById(id);
    if (!element?.fabricObject) return;

    element.locked = !element.locked;
    const locked = element.locked;

    element.fabricObject.set({
      selectable: !locked,
      lockMovementX: locked,
      lockMovementY: locked,
      lockRotation: locked,
      lockScalingX: locked,
      lockScalingY: locked,
    });

    this.store.setSelectedElement(null);
    this.canvas.renderAll();
  }

  // ==================== 克隆操作 ====================

  /**
   * 克隆元素
   */
  cloneElement(id: string) {
    const element = this._findElementById(id);
    if (!element || !this._ensureCanvas()) return;

    // 同步当前元素的fabric对象状态到element
    this.store.elementManager.syncFabricObjectToElement(element);

    // 创建克隆元素
    const clonedElement = JSON.parse(JSON.stringify(element));
    clonedElement.id = this._generateUid();
    clonedElement.name = `${element.name} (copy)`;

    // 调整位置避免重叠
    if (clonedElement.placement) {
      clonedElement.placement.x += ELEMENT_OPERATION_CONSTANTS.CLONE_OFFSET;
      clonedElement.placement.y += ELEMENT_OPERATION_CONSTANTS.CLONE_OFFSET;
    }

    // 清除fabric对象引用，让它重新创建
    clonedElement.fabricObject = undefined;

    // 处理不同类型元素的特殊克隆逻辑
    this._handleSpecialCloning(element, clonedElement);

    // 添加克隆元素到store
    this.store.addEditorElement(clonedElement);
  }

  // ==================== 私有辅助方法 ====================

  private _findElementById(id: string): EditorElement | undefined {
    return this.store.editorElements.find((el) => el.id === id);
  }

  private _ensureCanvas(): boolean {
    return !!this.canvas;
  }

  private _createPlacementFromFabricObject(fabricObject: fabric.Object): Partial<Placement> {
    return {
      x: fabricObject.left,
      y: fabricObject.top,
      width: fabricObject.width,
      height: fabricObject.height,
      rotation: fabricObject.angle,
      scaleX: fabricObject.scaleX,
      scaleY: fabricObject.scaleY,
      flipX: fabricObject.flipX,
      flipY: fabricObject.flipY,
    };
  }

  private _generateUid(): string {
    // 简单的UID生成器，实际项目中应该使用更robust的实现
    return Math.random().toString(36).substr(2, 9);
  }

  /**
   * 处理特殊元素类型的克隆逻辑
   */
  private _handleSpecialCloning(originalElement: EditorElement, clonedElement: EditorElement) {
    if (originalElement.type === "video" || originalElement.type === "audio") {
      this._cloneMediaElement(originalElement, clonedElement);
    } else if (originalElement.type === "image") {
      this._cloneImageElement(originalElement, clonedElement);
    } else if (originalElement.type === "gif") {
      this._cloneGifElement(originalElement, clonedElement);
    }
  }

  private _cloneMediaElement(originalElement: EditorElement, clonedElement: EditorElement) {
    const originalElementId = originalElement.properties.elementId;
    const newElementId = originalElementId.replace(originalElement.id, clonedElement.id);
    clonedElement.properties.elementId = newElementId;

    // 克隆媒体DOM元素
    const originalMedia = document.getElementById(originalElementId);
    if (originalMedia) {
      const clonedMedia = originalMedia.cloneNode(true) as HTMLElement;
      clonedMedia.id = newElementId;
      document.body.appendChild(clonedMedia);

      // 保持媒体元素的播放速度和音量
      if (originalMedia instanceof HTMLMediaElement) {
        const clonedMediaElement = clonedMedia as HTMLMediaElement;
        clonedMediaElement.playbackRate = originalMedia.playbackRate;
        clonedMediaElement.volume = originalMedia.volume;
        clonedMediaElement.currentTime = originalMedia.currentTime;

        // 复制自定义属性
        this._copyCustomMediaProperties(originalElement, clonedElement);
      }
    }
  }

  private _cloneImageElement(originalElement: EditorElement, clonedElement: EditorElement) {
    const originalElementId = originalElement.properties.elementId;
    const newElementId = originalElementId.replace(originalElement.id, clonedElement.id);
    clonedElement.properties.elementId = newElementId;

    // 克隆图片DOM元素
    const originalImage = document.getElementById(originalElementId);
    if (originalImage) {
      const clonedImage = originalImage.cloneNode(true) as HTMLElement;
      clonedImage.id = newElementId;
      document.body.appendChild(clonedImage);
    }
  }

  private _cloneGifElement(originalElement: EditorElement, clonedElement: EditorElement) {
    const originalElementId = originalElement.properties.elementId;
    const newElementId = originalElementId.replace(originalElement.id, clonedElement.id);
    clonedElement.properties.elementId = newElementId;

    // 克隆GIF DOM元素
    const originalGif = document.getElementById(originalElementId);
    if (originalGif) {
      const clonedGif = originalGif.cloneNode(true) as HTMLElement;
      clonedGif.id = newElementId;
      document.body.appendChild(clonedGif);
    }
  }

  private _copyCustomMediaProperties(originalElement: EditorElement, clonedElement: EditorElement) {
    const originalPlaybackSpeed = (originalElement as any).playbackSpeed;
    const originalVolume = (originalElement as any).volume;
    const originalMediaStartTime = (originalElement.properties as any).mediaStartTime;

    if (originalPlaybackSpeed !== undefined) {
      (clonedElement as any).playbackSpeed = originalPlaybackSpeed;
    }
    if (originalVolume !== undefined) {
      (clonedElement as any).volume = originalVolume;
    }
    if (originalMediaStartTime !== undefined) {
      (clonedElement.properties as any).mediaStartTime = originalMediaStartTime;
    }
  }
}
