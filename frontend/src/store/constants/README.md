# 常量管理系统

本目录包含了整个 store 模块的统一常量管理系统，旨在消除代码中的魔法数字和重复的常量定义。

## 文件结构

```
constants/
├── index.ts          # 主要的常量定义文件
└── README.md         # 本说明文件
```

## 常量分类

### 1. 画布相关常量 (CANVAS_CONSTANTS)
- 默认尺寸：宽度、高度、背景色
- 缩放限制：最小/最大缩放、默认缩放、缩放步长
- 窗口适配：缩放因子、UI偏移高度

### 2. 时间线相关常量 (TIMELINE_CONSTANTS)
- 时间限制：最大时间、默认持续时间等
- 元素默认持续时间：文本、图片、形状等
- 动画相关：FPS、滚轮节流间隔
- 平移相关：基础因子、滚动因子等
- UI尺寸：时间线高度、轨道高度

### 3. 媒体元素相关常量 (MEDIA_CONSTANTS)
- 默认尺寸：视频、图片、形状等的默认大小
- 视频录制：帧率、录制尺寸
- 支持格式：视频格式列表、默认格式

### 4. 元素类型常量 (ELEMENT_TYPES)
- 定义所有支持的元素类型：video、audio、image、gif、text、shape

### 5. 颜色主题常量 (COLOR_CONSTANTS)
- 元素类型颜色：不同元素类型的主题色
- 轨道类型颜色：不同轨道类型的颜色
- 特殊颜色：字幕颜色、吸附颜色等

### 6. 动画相关常量 (ANIMATION_CONSTANTS)
- 默认配置：过渡类型、精度位数
- 动画类型映射：前端动画类型到后端类型的映射
- 动画参数：旋转角度、循环次数、抖动强度等

### 7. 项目保存相关常量 (SAVE_CONSTANTS)
- 防抖延迟、项目键名
- 时间延迟：完成延迟、回退延迟

### 8. 元素操作相关常量 (ELEMENT_OPERATION_CONSTANTS)
- 缩放阈值、克隆偏移、默认边框半径
- 元素间距

### 9. 拖拽和吸附常量 (DRAG_CONSTANTS, SNAP_CONSTANTS)
- 拖拽阈值、激活距离、容差等
- 吸附阈值、吸附颜色

### 10. 字幕默认样式常量 (CAPTION_STYLE_CONSTANTS)
- 字体相关：大小、家族、颜色、权重等
- 描边和阴影：宽度、颜色、模糊等
- 背景和渐变：背景色、渐变设置
- 位置和变换：默认位置、缩放等
- 文本框相关：内边距、边框半径、光标等

### 11. UI状态常量 (UI_CONSTANTS)
- 默认菜单选项、编辑模式
- 加载状态默认值

### 12. 历史记录常量 (HISTORY_CONSTANTS)
- 操作类型：元素添加、删除、修改等

### 13. 形状类型常量 (SHAPE_TYPES)
- 所有支持的形状类型

## 使用方式

### 基本使用
```typescript
import { CONSTANTS } from './constants';

// 使用画布常量
const width = CONSTANTS.CANVAS.DEFAULT_WIDTH;
const height = CONSTANTS.CANVAS.DEFAULT_HEIGHT;

// 使用时间线常量
const maxTime = CONSTANTS.TIMELINE.MAX_TIME;
const defaultDuration = CONSTANTS.TIMELINE.DEFAULT_DURATION;
```

### 分类导入
```typescript
import { 
  CANVAS_CONSTANTS,
  TIMELINE_CONSTANTS,
  COLOR_CONSTANTS 
} from './constants';

const canvasWidth = CANVAS_CONSTANTS.DEFAULT_WIDTH;
const elementColor = COLOR_CONSTANTS.ELEMENT_COLORS.text;
```

### 向后兼容
为了保持向后兼容性，我们还提供了 `LEGACY_CONSTANTS` 导出，它保持了原有的常量结构：

```typescript
import { LEGACY_CONSTANTS } from './constants';

// 这与原来的使用方式相同
const width = LEGACY_CONSTANTS.CANVAS.DEFAULT_WIDTH;
```

## 迁移指南

### 已更新的文件
以下文件已经更新为使用统一的常量管理：

1. **Store.ts** - 主要的 Store 类
2. **ElementManager.ts** - 元素管理器
3. **TimelineManager.ts** - 时间线管理器
4. **ProjectManager.ts** - 项目管理器
5. **TrackManager.ts** - 轨道管理器
6. **CanvasManager.ts** - 画布管理器
7. **CaptionManager.ts** - 字幕管理器
8. **VideoSaver.ts** - 视频保存器
9. **frontend/src/editor/timeline/constants.ts** - 时间线常量文件

### 迁移步骤
1. 导入统一的常量：`import { CONSTANTS } from './constants';`
2. 替换硬编码的数值为对应的常量
3. 删除重复的常量定义
4. 更新类型定义（如果需要）

## 维护指南

### 添加新常量
1. 在 `index.ts` 中找到合适的常量分类
2. 添加新的常量定义
3. 更新 `CONSTANTS` 统一导出对象
4. 如果需要向后兼容，更新 `LEGACY_CONSTANTS`

### 修改现有常量
1. 在 `index.ts` 中修改常量值
2. 确保所有使用该常量的地方都能正常工作
3. 运行测试确保没有破坏性变更

### 最佳实践
1. 使用描述性的常量名称
2. 为常量添加注释说明用途
3. 将相关的常量分组管理
4. 避免在常量中使用魔法数字
5. 保持常量的不可变性（使用 `as const`）

## 注意事项

1. 所有常量都使用 `as const` 确保类型安全和不可变性
2. 常量名称使用 UPPER_SNAKE_CASE 命名规范
3. 分类常量使用 PascalCase 命名规范
4. 保持向后兼容性，避免破坏性变更
5. 定期检查和清理未使用的常量
